<template>
  <div class="exchange-market-container">
    <div class="exchange-market-wrapper">
      <div class="exchange-market-header">
        <h2>{{ $t('市场') }}</h2>
        <div class="close-btn" @click="emit('close')">
          <MonoClose size="16" class="fit-tc-primary" />
        </div>
        <div class="exchange-markets-search">
          <el-input
            ref="searchInputRef"
            v-model="search"
            :placeholder="$t('搜索')"
            clearable
            class="search-input-box">
            <template #prepend>
              <MonoSearch size="16" />
            </template>
          </el-input>
        </div>
      </div>
      <div class="level1Box">
        <ul class="flex-box">
          <li :class="{'active': firstActive === 'collect'}" @click="changeTab(1, 'collect')">{{ $t('自选') }}</li>
          <li v-for="(item, firstIndex) in firstTab" :key="firstIndex" :class="{'active': firstActive === item.id}" @click="changeTab(1, item.id)">{{ item.route_name }}</li>
        </ul>
      </div>
      <div v-if="secondTab.length > 0" class="level2Box">
        <el-tabs
          v-model="secondActive"
          >
          <el-tab-pane
            v-for="(item, secondIndex) in secondTab"
            :key="secondIndex"
            :label="item.route_name"
            :name="item.id"
          ></el-tab-pane>
        </el-tabs>
      </div>
      <!--<div v-if="secondTab.length > 0" class="level2Box">
        <BoxXOverflow class="flex-box" tag="ul">
          <li v-for="(item, secondIndex) in secondTab" :key="secondIndex" :class="{'active': secondActive === item.id}" @click="changeTab(2, item.id)">{{ item.route_name }}</li>
        </BoxXOverflow>
      </div>-->
      <div class="markets_wrap-content">
        <el-table :data="currentListFormat" :row-class-name="getRowClass" @row-click="changePair">
          <template #empty>
            <div style="height:300px;">
              <BoxNoData :text="$t('暂无数据')" />
            </div>
          </template>
          <el-table-column prop="pair" :label="$t('交易对')">
            <template #default="scope">
              <div class="flex-box">
                <div class="collet-icon" @click="selectCollect(scope.row)">
                  <MonoCollected v-if="favoriteMap[scope.row.pair]" size="16" class="fit-theme" />
                  <MonoCollect v-else size="16" class="fit-tc-primary" />
                </div>
                <span v-if="scope.row.pair.includes('_SWAP')" class="flex-box ts-12 tw-5 fit-tc-primary mg-l4" style="white-space:nowrap;">
                  {{ scope.row.pair.replace('_', '').replace('_SWAP', '') }}
                  <span class="font-size-12 fit-tc-secondary mg-l4">{{ $t('永续') }}</span>
                </span>
                <span v-else class="flex-box ts-12 tw-5 fit-tc-primary mg-l4" style="white-space:nowrap;">
                  {{ scope.row.pair.replace('_', '/') }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="last" :label="$t('价格')" sortable align="right" width="110" :sort-method="(f, b) => sort(f, b, 'last')">
            <template #default="scope">
              <span class="tw-5 ts-12 fit-tc-primary">{{ format((markets[scope.row.pair] ? markets[scope.row.pair].last : scope.row.last), (pairInfo[scope.row.pair] || {}).price_scale, true) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="change" :label="$t('涨幅')" sortable align="right" width="100" :sort-method="(f, b) => sort(f, b, 'percent')">
            <template #default="scope">
              <span :class="getChangeClass(scope.row)" class="tw-4 ts-12">{{ markets[scope.row.pair] ? format((markets[scope.row.pair].change * 100), 2, true) : format((scope.row.change * 100), 2, true) }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { format } from '~/utils'
  import { ElInput, ElTable, ElTabs, ElTabPane } from 'element-plus'
  import { getPairAreas, getAllPairs } from '~/api/public'
  import { commonStore } from '~/stores/commonStore'
  import BoxXOverflow from '~/components/common/BoxXOverflow.vue'
  const store = commonStore()
  const { subAllTickerSocket, cancelAllTicker } = store
  const { marketsObj, pairInfo } = storeToRefs(store)
  const { locale, t } = useI18n()
  const router = useRouter()
  const props = defineProps({
    pair: {
      type: String,
      default: ''
    },
    favoriteMap: {
      type: Object,
      default () {
        return {}
      }
    },
    isFuture: {
      type: Boolean,
      default: false
    }
  })
  const emit = defineEmits(['close', 'selectCollect', 'changePair'])
  const search = ref('')
  const getChangeClass = (item) => {
    if (!item) {
      return false
    }
    const number = Number(item.change)
    if (number > 0) {
      return 'fit-rise'
    }
    return 'fit-fall'
  }
  const requestAnimationFrameInterval = ref(null)
  const firstActive = ref('')
  const secondActive = ref('')
  const allAreaList = ref([])
  const allPairsList = ref([])
  const spotList = ref([])
  const futureList = ref([])
  const markets = ref({})
  const currentListFormat = computed(() => {
    if (allPairsList.value.length > 0) {
      if (firstActive.value === 'collect') {
        const collectList = allPairsList.value.filter((item) => {
          return search.value !== '' ? item.pair.replace('_', '/').includes(search.value.toUpperCase()) && props.favoriteMap[item.pair] : props.favoriteMap[item.pair]
        })
        if (secondActive.value === 'spot') {
          return spotList.value.filter((item) => {
            return search.value !== '' ? item.pair.replace('_', '/').includes(search.value.toUpperCase()) && props.favoriteMap[item.pair] : props.favoriteMap[item.pair]
          })
        } else if (secondActive.value === 'future') {
          return futureList.value.filter((item) => {
            return props.favoriteMap[item.pair]
          })
        } else {
          return collectList
        }
      } else {
        const arr = allPairsList.value.filter((item) => {
          return (secondTab.value ? (item.area_id  === secondActive.value) : item.area_id === firstActive.value)
        })
        if (search.value !== '') {
          return arr.filter((item) => {
            return item.pair.replace('_', '/').includes(search.value.toUpperCase())
          })
        } else {
          return arr
        }
      }
    }
    return []
  })
  const firstTab = computed(() => {
    if (allAreaList.value.length > 0) {
      return allAreaList.value.filter((item) => {
        return item.level * 1 === 1
      })
    }
    return []
  })
  const secondTab = computed(() => {
    if (firstActive.value === 'collect') {
      return [
        { id: '', route_name: t('全部') },
        { id: 'spot', route_name: t('现货') },
        { id: 'future', route_name: t('合约') }
      ]
    }
    if (allAreaList.value.length > 0) {
      return allAreaList.value.filter((item) => {
        return item.level * 1 === 2 && item.father_area_id === firstActive.value
      })
    }
    return []
  })
  watch(() => firstActive.value, (val) => {
    if (val === 'collect') {
      secondActive.value = ''
    } else {
      secondActive.value = secondTab.value.length > 0 ? secondTab.value[0].id : ''
    }
  })
  const changeTab = (level, id) => {
    if (level === 1 && id === 'collect') {
      firstActive.value = 'collect'
    } else if (level === 1 && id !== 'collect') {
      firstActive.value = id
    } else {
      secondActive.value = id
    }
  }
  const selectCollect = (item) => {
    emit('selectCollect', item)
  }
  const getAllPairList = async() => {
    const { data } = await getAllPairs()
    if (data) {
      spotList.value = data.spot
      futureList.value = data.contract
      allPairsList.value = data.spot.concat(data.contract)
    }
  }
  const getAllTabList = async() => {
    const { data } = await getPairAreas({
      lang: locale.value
    })
    if (data) {
      allAreaList.value = data
      const secondArr = data.filter((item) => {
        return item.level * 1 === 2 && item.father_area_id === firstActive.value
      })
      if (props.isFuture) {
        firstActive.value = data.filter((item) => {
          return item.level * 1 === 1
        })[1].id
        secondActive.value = secondArr && secondArr[1] && secondArr[1].id || ''
      } else {
        firstActive.value = data.filter((item) => {
          return item.level * 1 === 1
        })[0].id
        secondActive.value = secondArr && secondArr[1] && secondArr[1].id || ''
      }
    }
  }
  const getRowClass = ({ row = {} }) => {
    return (row.pair === props.pair) ? 'active cursor-pointer' : ' cursor-pointer'
  }
  const sort = (a, b, key) => {
    if (key === 'last') {
      return (markets.value[a.pair]?.last || a.last) - (markets.value[b.pair]?.last || b.last)
    } else if (key === 'percent') {
      return (markets.value[a.pair]?.change || a.change) - (markets.value[b.pair]?.change || b.change)
    }
    return 0
  }
  const changePair = (row) => {
    console.log(`=== ExchangeMarketsDropdown changePair ===`)
    console.log(`用户点击币种: ${row.pair}`)
    emit('changePair', row.pair)
    emit('close')
  }
  const socketDateAnimation = () => {
    markets.value = marketsObj.value
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  }
  onMounted(() => {
    subAllTickerSocket()
    getAllTabList()
    getAllPairList()
    socketDateAnimation()
    requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
  })
  onBeforeUnmount(() => {
    cancelAllTicker()
    requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
  })
</script>
<style lang="scss">
.exchange-market-container{
  height:100%;
  overflow:hidden;
  .exchange-market-wrapper{
    height:100%;
    @include bg-color(bg-primary);
    .exchange-market-header{
      padding:20px 20px 0;
      h2, .close-btn{
        display:none;
      }
      .exchange-markets-search{
        .el-input{
          &.search-input-box{
            .el-input__wrapper{
              border-radius:4px;
            }
            .el-input__inner{
              height:36px !important;
            }
          }
        }
      }
    }
    .level1Box, .level2Box{
      margin:0 20px;
      font-size:14px;
    }
    .level1Box{
      border-bottom:1px solid;
      @include border-color(border);
      ul{
        height:40px;
        li{
          height:40px;
          line-height:40px;
          margin-right:20px;
          cursor:pointer;
          position:relative;
          font-size:14px;
          @include color(tc-secondary);
          &.active{
            @include color(tc-primary);
            &:after{
              content: '';
              position:absolute;
              display:block;
              bottom:0;
              left:50%;
              margin-left:-8px;
              width:16px;
              height:2px;
              @include bg-color(theme);
            }
          }
        }
      }
    }
    .level2Box{
      .el-tabs__header{
        margin:4px 0;
        .el-tabs__nav-wrap{
          &.is-scrollable{
            padding:0 20px 0 0;
          }
          &:after{
            display:none;
          }
          .el-tabs__nav-prev{
            z-index:9;
            left:-4px;
            @include bg-color(bg-dialog);
            &.is-disabled{
              display:none;
            }
          }
          .el-tabs__nav{
            .el-tabs__active-bar{
              display:none;
            }
            .el-tabs__item{
              margin:8px 0;
              height:auto;
              padding:2px 10px;
              @include color(tc-secondary);
              &.is-active{
                border-radius:4px;
                border:1px solid;
                background: rgba(240, 185, 11, 0.1);
                @include border-color(theme);
                @include color(theme);
              }
            }
          }
        }
      }
      ul{
        height:46px;
        li{
          padding:4px 12px;
          border-radius:6px;
          margin-right:2px;
          cursor:pointer;
          font-size:12px;
          @include color(tc-secondary);
          &.active{
            border:1px solid;
            background: rgba(240, 185, 11, 0.1);
            @include border-color(theme);
            @include color(theme);
          }
        }
      }
    }
    .markets_wrap-content{
      height:calc(100% - 144px);
      overflow-y:auto;
      .el-table{
        th{
          @include color(tc-secondary);
        }
        th,td{
          border-bottom:0;
        }
        tr{
          cursor:pointer;
          &.active{
            td{
              @include bg-color(bg-quaternary);
            }
          }
        }
      }
    }
  }
}
@include mb {
  .exchange-market-container{
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    z-index:99999;
    background:rgba(0,0,0,0.5);
    display:flex;
    align-items: flex-end;
    .exchange-market-wrapper{
      height:80%;
      width:100%;
      border-top-right-radius:20px;
      border-top-left-radius:20px;
      position:relative;
      @include bg-color(bg-primary);
      .exchange-market-header{
        padding:20px 20px 0;
        h2, .close-btn{
          display:block;
        }
        h2{
          font-size:18px;
          padding-bottom:12px;
          @include color(tc-primary);
        }
        .close-btn{
          position:absolute;
          top:16px;
          right:20px;
          font-size:16px;
          @include color(tc-primary);
        }
      }
      .markets_wrap-content{
        height:calc(100% - 183px);
      }
    }
  }
}
</style>
