<template>
  <Header modeType="spot" />
  <Message  />
  <div class="exchange-container">
    <div class="exchange-wrapper">
      <div name="left" class="cont-wrap-bg left"></div>
      <div name="right" class="cont-wrap-bg right"></div>
      <div name="subHeader" class="cont-wrap-bg subHeader">
        <!-- 二级头部 -->
        <ExchangeSubHeader
          :pair="pair"
          :ticker="tickers[pair]"
          :coinInfo="coinInfo"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          @changePair="changePair"
        />
        <!-- 二级头部 -->
      </div>
      <div name="trades" class="cont-wrap-bg trades">
        <!--交易盘口-->
        <ExchangeTrades
          :pair="pair"
          :propsDepth="depths"
          :ticker="tickers[pair]"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          @change-price="changePrice"
          @change-amount="changeAmount" />
        <!--交易盘口-->
      </div>
      <div v-if="landingPairs[pair]" name="chart" class="cont-wrap-bg chart">
        <ExchangeLandingPair :data="landingPairs[pair]" :coinInfo="coinInfo" @timerEnd="timerEnd" />
      </div>
      <div v-else name="chart" class="cont-wrap-bg chart">
        <ClientOnly>
          <ExchangeCharts
            :pair="pair" 
            :propsDepth="depths"
            :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
            :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
            :ticker="tickers[pair]"
            :coinInfo="coinInfo"
            @change-price="changePrice"
            @change-amount="changeAmount"
            @changePeriod="changePeriod"  />
          </ClientOnly>
      </div>
      <div name="orderform" class="cont-wrap-bg orderform">
        <ExchangeOrderform
          :isLogin="isLogin"
          :pair="pair"
          :amount="amount"
          :price="price"
          :ticker="tickers[pair]"
          :tradeAssetObj="tradeAssetObj"
          :pairInfo="pairInfo"
          :priceScale="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
          :quantityScale="typeof (pairInfo[pair] || {}).quantity_scale === 'number' && !isNaN((pairInfo[pair] || {}).quantity_scale) ? (pairInfo[pair] || {}).quantity_scale : 2"
          :pairPricePrecision="(pairInfo[pair] || {}).price_scale ? (pairInfo[pair] || {}).price_scale : 4"
        />
      </div>
      <div name="orders" class="cont-wrap-bg orders">
        <ExchangeOrders :isLogin="isLogin" :pair="pair" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '@/utils/index'
import { cookies } from '~/utils/cookies'
import Message from '~/components/message/index.vue'
import ExchangeSubHeader from '~/components/exchange/ExchangeSubHeader.vue'
import ExchangeTrades from '~/components/exchange/ExchangeTrades.vue'
import ExchangeOrderform from '~/components/exchange/ExchangeOrderform.vue'
import ExchangeOrders from '~/components/exchange/ExchangeOrders.vue'
import ExchangeCharts from '~/components/exchange/ExchangeCharts.vue'
import ExchangeLandingPair from '~/components/exchange/ExchangeLandingPair.vue'
import { commonStore } from '~/stores/commonStore'
import { getCoinParams } from '~/api/public'
import { useCommonData } from '~/composables/index'
import { useUserStore } from '~/stores/useUserStore'
const { locale, t } = useI18n()
const useCommon = useCommonData()
const store = commonStore()
const userStore = useUserStore()
const { isLogin, userInfo } = storeToRefs(userStore)
const { getAssetsByCoin, cancelSocket, cancelKline, getDepthSocket, subTradesSocket, subTickerSocket, subLogin, getLandingPairs, reConnectUser } = store
const { tradeAssetObj, depthsStore, chartDepth, ticker, pairInfo, landingPairs } = storeToRefs(store)
const router = useRouter()
const requestAnimationFrameInterval = ref(null)
const exchangeDepths = ref({})
const isShowDepths = ref(false)
const amount = ref('')
const price = ref('')
const depths = ref({})
const deals = ref({})
const tickers = ref({})
const pair = ref('')
const oldPair = ref('')
const coinInfo = ref({})
const safeJsonParse = (jsonStr) => {
  // 如果输入已经是对象，直接返回
  if (typeof jsonStr === 'object') return jsonStr;

  try {
    // 首次尝试直接解析
    return JSON.parse(jsonStr);
  } catch (initialError) {
    try {
      // 深度修复函数 - 特别处理混合引号问题
      const deeplyFixJson = (str) => {
        // 1. 首先标准化引号：将中文全角引号替换为转义后的半角引号
        let fixed = str
          .replace(/“/g, '\\"')  // 中文左双引号
          .replace(/”/g, '\\"')  // 中文右双引号
          .replace(/‘/g, "'")    // 中文左单引号
          .replace(/’/g, "'");   // 中文右单引号

        // 2. 处理文本中未转义的英文双引号（保留属性名的引号）
        fixed = fixed.replace(/"([^"\\]*(\\.[^"\\]*)*)"(?=\s*[,\]}])/g, (match) => {
          // 跳过键名部分的引号（后面跟着冒号的）
          if (/:/.test(match)) return match;
          // 处理值部分的引号
          return match.replace(/([^\\])"/g, '$1\\"');
        });

        // 3. 处理所有常见的转义问题
        fixed = fixed
          .replace(/\\'/g, "'")        // 单引号
          .replace(/\\"/g, '"')        // 双引号
          .replace(/\\n/g, '\n')       // 换行符
          .replace(/\\r/g, '\r')       // 回车符
          .replace(/\\t/g, '\t')      // 制表符
          .replace(/\\b/g, '\b')      // 退格符
          .replace(/\\f/g, '\f')       // 换页符
          .replace(/\\u2028/g, '\u2028')  // 行分隔符
          .replace(/\\u2029/g, '\u2029'); // 段落分隔符

        // 4. 处理未闭合的数组或对象
        const trimmed = fixed.trim();
        if (!trimmed.endsWith(']') && !trimmed.endsWith('}')) {
          if (trimmed.startsWith('[')) fixed += ']';
          else if (trimmed.startsWith('{')) fixed += '}';
        }

        // 5. 处理多余的逗号
        fixed = fixed.replace(/,\s*([}\]])/g, '$1');

        // 6. 处理控制字符和空格
        fixed = fixed
          .replace(/[\u0000-\u001F]/g, '') // 移除控制字符
          .replace(/\s+/g, ' ')           // 标准化空格
          .replace(/"\s+:/g, '":')        // 修复键后的空格
          .replace(/:\s+"/g, ':"');       // 修复值前的空格

        return fixed;
      };

      // 尝试修复并解析
      const fixedJson = deeplyFixJson(jsonStr);
      let result = JSON.parse(fixedJson);

      // 如果结果仍然是字符串，可能包含嵌套JSON
      if (typeof result === 'string' && (result.startsWith('{') || result.startsWith('['))) {
        try {
          result = JSON.parse(result);
        } catch (nestedError) {
          // 如果嵌套解析失败，尝试再次修复
          result = JSON.parse(deeplyFixJson(result));
        }
      }

      return result;
    } catch (finalError) {
      console.error('自动修复失败，原始数据:', jsonStr);
      throw new Error(`JSON解析失败: ${finalError.message}`);
    }
  }
}
const getCoinIfon = async() => {
  coinInfo.value = {}
  const { data } = await getCoinParams({
    coin_symbol: pair.value.split('_')[0]
  })
  if (data) {
    const depInfo = {}
    const lang = {
      'zh-cn': 'zh',
      'en-ww': 'en'
    }
    coinInfo.value = data
    if (data.describe_summary !== '') {
      safeJsonParse(String(data.describe_summary).trim()).forEach((item) => {
        depInfo[lang[item.lang]] = item.text
      })
      data['decription'] = depInfo
    }
  }
}
watch(() => userInfo.value, (val) => {
  if (JSON.stringify(val) !== '{}') {
    getAssetsByCoin()
    subLogin()
  }
}, {
  immediate: true
})
const changeAmount = (amountP) => {
  amount.value = amountP
}
const timerEnd = () => {
  getLandingPairs()
}
const changePrice = (priceP) => {
  price.value = Number(priceP).toFixed((pairInfo.value[pair.value] || {}).price_scale)
}
const changePair = (p) => {
  console.log(`=== exchange页面 changePair ===`)
  console.log(`接收到币种切换: ${p}`)
  console.log(`当前pair.value: ${pair.value}`)

  if (p.includes('_SWAP')) {
    router.push(`/${locale.value}/future/${p}`)
  } else {
    console.log(`设置pair.value为: ${p}`)
    pair.value = p;
    nextTick(() => {
      window.history.replaceState({}, null, `/${locale.value}/exchange/${p}`)
    })
    router.currentRoute.value.params.pair = p;
  }
}
watch(() => pair.value, (nv, ov) => {
  console.log(`=== exchange页面 pair.value变化 ===`)
  console.log(`从 ${ov} 切换到 ${nv}`)

  oldPair.value = ov ? ov : pair.value
  if (ov) {
    cancelSocket(ov);
    cancelKline(ov, oldPeriod.value)
  }
  nextTick(() => {
    sub(nv)
    getCoinIfon()
  })
})
const socketDateAnimation = () => {
  depths.value = depthsStore.value
  tickers.value = ticker.value
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
}
const sub = (p) => {
  if (oldPair.value) {
    cancelSocket(oldPair.value); // 取消旧交易对的订阅
  }
  subTickerSocket(p)
  subTradesSocket(p)
  getDepthSocket(p)
}
const oldPeriod = ref('')
const curPeriod = ref('')
const lastShowTime = ref(0)
const changePeriod = (Period) => {
  oldPeriod.value = curPeriod.value
  curPeriod.value = Period
}
const onVisibilityChange = () => {
  const isHidden = document.isHidden
  if (!isHidden && isLogin.value && new Date().getTime() - lastShowTime.value >= 1000 * 60) {
    reConnectUser(pair.value)
  } else {
    lastShowTime.value = new Date().getTime()
  }
}
onMounted(() => {
  document.addEventListener('visibilitychange', onVisibilityChange)
  oldPeriod.value = getStorage('exchange-period')
  curPeriod.value = getStorage('exchange-period')
})
onBeforeMount(() => {
  pair.value = router.currentRoute.value.params.pair
  oldPair.value = router.currentRoute.value.params.pair
  sub(pair.value)
  getLandingPairs()
  socketDateAnimation()
  requestAnimationFrameInterval.value = requestAnimationFrame(socketDateAnimation)
})
onBeforeUnmount(() => {
  cancelKline(pair.value, curPeriod.value)
  cancelSocket(pair.value)
  window.removeEventListener('visibilitychange', onVisibilityChange)
  requestAnimationFrameInterval.value && cancelAnimationFrame(requestAnimationFrameInterval.value)
})
useHead({
  title: () => `${tickers.value[pair.value]?.last || '--'} ${pair.value.replace('_', '/')} ${t('现货交易')}` || `${t('安全合规的数字资产交易平台,对meme币最友好的交易所')}`,
  titleTemplate: `${t('KTX')} | %s`,
  meta: [
    { hid: 'description', name: 'description', content: t('安全合规的数字资产交易平台,对meme币最友好的交易所') },
    { hid: 'keywords', name: 'keywords', content: t('KTX、区块链、数字货币、比特币、莱特币、以太坊，交易所，OSL') }
  ]
});
</script>
<style lang="scss">
@import url('@/assets/style/exchange/index.scss');
</style>